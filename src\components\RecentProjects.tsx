import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Volume2, VolumeX } from "lucide-react";
import { useState, useRef } from "react";

const RecentProjects = () => {
  // Video Player State Management
  // Reason for State: Control video playback, mute state, and lazy loading for heavy video files
  // Task Performed: Manages individual video playback, audio controls, and loading states
  // Linking Information: Internal - Used by video elements and control buttons in cards
  const [playingVideos, setPlayingVideos] = useState<Set<number>>(new Set());
  const [mutedVideos, setMutedVideos] = useState<Set<number>>(new Set());
  const [loadedVideos, setLoadedVideos] = useState<Set<number>>(new Set());
  const [loadingVideos, setLoadingVideos] = useState<Set<number>>(new Set());
  const videoRefs = useRef<{ [key: number]: HTMLVideoElement | null }>({});

  // Success Stories Video Data from Supabase
  // Reason for Data: Store video information for success stories using direct Supabase video URLs
  // Task Performed: Defines project data with direct video URLs from Supabase storage
  // Linking Information: External - Links to Supabase storage videos, Internal - Used by video cards
  const projects = [
    {
      id: 1,
      title: "Kombucha Pin Marketing Video",
      platform: "Pinterest",
      category: "Food & Beverage",
      fileName: "kombucha pin 3.mp4",
      videoUrl: "https://mvfmyzhxynhgspfmonox.supabase.co/storage/v1/object/public/sample-videos/kombucha pin 3.mp4",
      duration: "30s",
      views: "450K",
      engagement: "16.8%",
      description: "Engaging kombucha marketing content with vibrant visuals and compelling product showcase."
    },
    {
      id: 2,
      title: "Guest Lists Management Video",
      platform: "Instagram",
      category: "Business",
      fileName: "guest lists.mp4",
      videoUrl: "https://mvfmyzhxynhgspfmonox.supabase.co/storage/v1/object/public/sample-videos/guest lists.mp4",
      duration: "45s",
      views: "320K",
      engagement: "14.2%",
      description: "Professional guest list management demonstration with clean interface design and functionality."
    },
    {
      id: 3,
      title: "Suboro Brand Showcase",
      platform: "TikTok",
      category: "Fashion",
      fileName: "suboro1.mp4",
      videoUrl: "https://mvfmyzhxynhgspfmonox.supabase.co/storage/v1/object/public/sample-videos/suboro1.mp4",
      duration: "60s",
      views: "680K",
      engagement: "19.5%",
      description: "Dynamic fashion brand showcase featuring trendy styles and modern aesthetic appeal."
    },
    {
      id: 4,
      title: "Fume Product Demonstration",
      platform: "YouTube",
      category: "Technology",
      fileName: "fume vid 3-1.mp4",
      videoUrl: "https://mvfmyzhxynhgspfmonox.supabase.co/storage/v1/object/public/sample-videos/fume vid 3-1.mp4",
      duration: "40s",
      views: "290K",
      engagement: "12.7%",
      description: "Innovative product demonstration highlighting key features and user benefits."
    },
    {
      id: 5,
      title: "Baali Dance Performance",
      platform: "Instagram",
      category: "Entertainment",
      fileName: "baali d re edit 11-21.mp4",
      videoUrl: "https://mvfmyzhxynhgspfmonox.supabase.co/storage/v1/object/public/sample-videos/baali d re edit 11-21.mp4",
      duration: "50s",
      views: "520K",
      engagement: "22.1%",
      description: "Captivating dance performance with professional editing and dynamic visual effects."
    },
    {
      id: 6,
      title: "Travel Reel Adventure",
      platform: "Instagram",
      category: "Travel",
      fileName: "travel reel re edit.mp4",
      videoUrl: "https://mvfmyzhxynhgspfmonox.supabase.co/storage/v1/object/public/sample-videos/travel reel re edit.mp4",
      duration: "35s",
      views: "410K",
      engagement: "18.9%",
      description: "Inspiring travel content showcasing beautiful destinations with cinematic storytelling."
    }
  ];

  // Platform Color Handler
  // Reason for Function: Assign platform-specific colors to badges
  // Task Performed: Returns appropriate CSS classes for platform badges
  // Linking Information: Internal - Used by platform badges in project cards
  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case "Instagram": return "bg-pink-500/10 text-pink-400 border-pink-500/20";
      case "YouTube":
      case "YouTube Shorts": return "bg-red-500/10 text-red-400 border-red-500/20";
      case "TikTok": return "bg-purple-500/10 text-purple-400 border-purple-500/20";
      case "Pinterest": return "bg-orange-500/10 text-orange-400 border-orange-500/20";
      default: return "bg-accent/10 text-accent border-accent/20";
    }
  };

  // Video Play/Pause Handler with Lazy Loading
  // Reason for Function: Handle play/pause toggle with lazy loading for heavy video files
  // Task Performed: Toggles video playback state, manages manual play only, and lazy loads videos
  // Linking Information: Internal - Used by play buttons and video elements in project cards
  const handleVideoToggle = async (projectId: number) => {
    const video = videoRefs.current[projectId];
    if (!video) return;

    if (playingVideos.has(projectId)) {
      // Pause video
      video.pause();
      setPlayingVideos(prev => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    } else {
      // Load and play video
      if (!loadedVideos.has(projectId)) {
        setLoadingVideos(prev => new Set(prev).add(projectId));

        try {
          // Lazy load the video source
          const project = projects.find(p => p.id === projectId);
          if (project && !video.src) {
            video.src = project.videoUrl;
          }

          // Wait for video to load
          await new Promise((resolve, reject) => {
            video.onloadeddata = resolve;
            video.onerror = reject;
            video.load();
          });

          setLoadedVideos(prev => new Set(prev).add(projectId));
        } catch (error) {
          console.error(`Error loading video ${projectId}:`, error);
        } finally {
          setLoadingVideos(prev => {
            const newSet = new Set(prev);
            newSet.delete(projectId);
            return newSet;
          });
        }
      }

      // Play video with sound enabled (user preference)
      video.muted = false;
      try {
        await video.play();
        setPlayingVideos(prev => new Set(prev).add(projectId));
      } catch (error) {
        // Fallback to muted if autoplay policy blocks unmuted play
        video.muted = true;
        setMutedVideos(prev => new Set(prev).add(projectId));
        await video.play();
        setPlayingVideos(prev => new Set(prev).add(projectId));
      }
    }
  };

  // Video Mute Handler
  // Reason for Function: Handle mute/unmute toggle for video audio control
  // Task Performed: Toggles video mute state with user preference for sound enabled
  // Linking Information: Internal - Used by mute buttons in video controls
  const handleMuteToggle = (projectId: number) => {
    const video = videoRefs.current[projectId];
    if (!video) return;

    if (mutedVideos.has(projectId)) {
      video.muted = false;
      setMutedVideos(prev => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    } else {
      video.muted = true;
      setMutedVideos(prev => new Set(prev).add(projectId));
    }
  };



  return (
    <section id="projects" className="py-20 bg-background relative overflow-hidden">
      {/* Premium Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-secondary/30 to-background"></div>
      <div className="absolute top-1/4 left-0 w-72 h-72 bg-gradient-start/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-0 w-72 h-72 bg-gradient-end/10 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        <div className="text-center mb-20">
          <Badge className="bg-hero-gradient text-white border-0 mb-6 px-6 py-3 text-sm font-semibold tracking-wide">
            🎬 Recent Projects
          </Badge>
          <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight">
            Our Latest
            <span className="bg-hero-gradient bg-clip-text text-transparent block mt-2">
              Success Stories
            </span>
          </h2>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            See how we've helped brands and creators achieve viral success with
            engaging short-form video content across all major platforms.
          </p>

          {/* Premium Divider */}
          <div className="flex items-center justify-center mt-8">
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
            <div className="mx-4 w-2 h-2 bg-accent rounded-full animate-pulse"></div>
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {projects.map((project) => (
            <Card
              key={project.id}
              className="bg-card-gradient border-service-border hover:border-accent/30 transition-all duration-500 hover:scale-105 group overflow-hidden relative"
            >
              {/* Premium Card Background Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-gradient-start/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="relative">
                {/* Direct Video Player Container - Vertical 9:16 Aspect Ratio */}
                <div className="relative overflow-hidden aspect-[9/16] w-full max-w-xs mx-auto bg-gray-900">
                  {/* Video Element with Lazy Loading */}
                  <video
                    ref={(el) => {
                      if (el) videoRefs.current[project.id] = el;
                    }}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                    loop
                    muted={mutedVideos.has(project.id)}
                    playsInline
                    preload="none"
                    poster=""
                  />

                  {/* Loading State Overlay */}
                  {loadingVideos.has(project.id) && (
                    <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                        <p className="text-white text-sm">Loading video...</p>
                      </div>
                    </div>
                  )}

                  {/* Video Thumbnail/Placeholder when not loaded */}
                  {!loadedVideos.has(project.id) && !loadingVideos.has(project.id) && (
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
                      <div className="text-center text-white">
                        <Play className="h-12 w-12 mx-auto mb-2 opacity-60" />
                        <p className="text-sm opacity-80">Click to load video</p>
                      </div>
                    </div>
                  )}

                  {/* Premium Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-40"></div>

                  {/* Video Controls Overlay - Only show when video is loaded */}
                  {loadedVideos.has(project.id) && (
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
                      <div className="flex gap-3">
                        {/* Play/Pause Button */}
                        <button
                          onClick={() => handleVideoToggle(project.id)}
                          disabled={loadingVideos.has(project.id)}
                          className="bg-white/95 backdrop-blur-sm rounded-full p-3 hover:scale-110 transition-transform duration-300 shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {playingVideos.has(project.id) ? (
                            <div className="h-5 w-5 text-primary">
                              <div className="flex gap-1">
                                <div className="w-1.5 h-5 bg-current"></div>
                                <div className="w-1.5 h-5 bg-current"></div>
                              </div>
                            </div>
                          ) : (
                            <Play className="h-5 w-5 text-primary ml-0.5" fill="currentColor" />
                          )}
                        </button>

                        {/* Mute/Unmute Button - Only show when playing */}
                        {playingVideos.has(project.id) && (
                          <button
                            onClick={() => handleMuteToggle(project.id)}
                            className="bg-white/95 backdrop-blur-sm rounded-full p-3 hover:scale-110 transition-transform duration-300 shadow-2xl"
                          >
                            {mutedVideos.has(project.id) ? (
                              <VolumeX className="h-5 w-5 text-primary" />
                            ) : (
                              <Volume2 className="h-5 w-5 text-primary" />
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Initial Play Button for Unloaded Videos */}
                  {!loadedVideos.has(project.id) && !loadingVideos.has(project.id) && (
                    <div className="absolute inset-0 flex items-center justify-center cursor-pointer" onClick={() => handleVideoToggle(project.id)}>
                      <div className="bg-white/95 backdrop-blur-sm rounded-full p-4 hover:scale-110 transition-transform duration-300 shadow-2xl">
                        <Play className="h-8 w-8 text-primary ml-1" fill="currentColor" />
                      </div>
                    </div>
                  )}

                  {/* Premium Corner Accent */}
                  <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-accent/20 to-transparent"></div>

                  <div className="absolute top-4 left-4">
                    <Badge className={`${getPlatformColor(project.platform)} shadow-lg backdrop-blur-sm`}>
                      {project.platform}
                    </Badge>
                  </div>
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-black/70 text-white border-0 backdrop-blur-sm shadow-lg">
                      {project.duration}
                    </Badge>
                  </div>
                </div>
              </div>

                {/* Enhanced Card Content */}
                <CardContent className="p-8 relative z-10">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold mb-3 text-foreground group-hover:text-accent transition-colors duration-300">
                        {project.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                        {project.description}
                      </p>
                    </div>

                    {/* Premium Stats Grid */}
                    <div className="grid grid-cols-2 gap-6">
                      <div className="text-center p-4 bg-gradient-to-br from-accent/10 to-transparent rounded-xl border border-accent/20 group-hover:border-accent/40 transition-colors duration-300">
                        <p className="text-2xl font-bold text-accent mb-1">
                          {project.views}
                        </p>
                        <p className="text-xs text-muted-foreground font-medium">Views</p>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-gradient-start/10 to-transparent rounded-xl border border-gradient-start/20 group-hover:border-gradient-start/40 transition-colors duration-300">
                        <p className="text-2xl font-bold text-gradient-start mb-1">
                          {project.engagement}
                        </p>
                        <p className="text-xs text-muted-foreground font-medium">Engagement</p>
                      </div>
                    </div>

                    {/* Premium Action Buttons */}
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleVideoToggle(project.id)}
                        disabled={loadingVideos.has(project.id)}
                        className="w-full bg-hero-gradient hover:opacity-90 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loadingVideos.has(project.id) ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                            Loading...
                          </>
                        ) : playingVideos.has(project.id) ? (
                          <>
                            <div className="mr-2 flex gap-0.5">
                              <div className="w-1 h-4 bg-current"></div>
                              <div className="w-1 h-4 bg-current"></div>
                            </div>
                            Pause Video
                          </>
                        ) : (
                          <>
                            <Play size={16} className="mr-2" />
                            {loadedVideos.has(project.id) ? 'Play Video' : 'Load & Play'}
                          </>
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Premium Bottom Accent */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-accent/50 via-gradient-start/50 to-accent/50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-center"></div>
                </CardContent>
              </Card>
          ))}
        </div>


      </div>
    </section>
  );
};

export default RecentProjects;